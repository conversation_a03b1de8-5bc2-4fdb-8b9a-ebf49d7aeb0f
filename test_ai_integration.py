#!/usr/bin/env python3
"""
Test script for AI integration endpoints
"""
import requests
import json

# Base URL for the FastAPI server
BASE_URL = "http://127.0.0.1:8000"

def test_ai_recommendations():
    """Test the AI recommendations endpoint with sample data"""
    
    # Sample analysis results
    test_data = {
        "analysis_results": {
            "yolo_predictions": [
                {
                    "label": "Flat Foot",
                    "confidence": 0.85,
                    "bbox": [100, 100, 200, 200]
                }
            ],
            "tensorflow_prediction": {
                "class": "Hallux Valgus",
                "confidence": 0.92
            }
        },
        "patient_info": {
            "age": 35,
            "weight": "70kg",
            "activity_level": "moderate",
            "symptoms": "foot pain after walking"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/ai-recommendations",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ AI recommendations endpoint working!")
        else:
            print("❌ AI recommendations endpoint failed")
            
    except Exception as e:
        print(f"❌ Error testing AI recommendations: {e}")

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/ping")
        print(f"Health check: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing AI Integration Endpoints")
    print("=" * 50)
    
    # Test health check first
    if test_health_check():
        print("✅ Server is running")
        print("\n🤖 Testing AI Recommendations...")
        test_ai_recommendations()
    else:
        print("❌ Server is not responding. Make sure FastAPI server is running.")
        print("Run: npm run fastapi-dev")
