import requests
import io
from PIL import Image
import numpy as np

# Create test image
img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
img = Image.fromarray(img_array)
img_bytes = io.BytesIO()
img.save(img_bytes, format='JPEG')
img_bytes.seek(0)

print("Testing flatfoot with precise AI...")
files = {'file': ('test.jpg', img_bytes, 'image/jpeg')}

try:
    response = requests.post("http://localhost:8000/predict/yolo", files=files, timeout=60)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Success!")
        
        if 'ai_recommendations' in result:
            ai_rec = result['ai_recommendations']
            print(f"AI Success: {ai_rec.get('success')}")
            print(f"Analysis Type: {ai_rec.get('analysis_type')}")
            
            if ai_rec.get('success'):
                recommendations = ai_rec.get('recommendations', '')
                print(f"Recommendations length: {len(recommendations)}")
                print("First 500 chars:")
                print(recommendations[:500])
            else:
                print(f"Error: {ai_rec.get('error')}")
                print(f"Fallback: {ai_rec.get('fallback_recommendations', '')[:200]}")
        else:
            print("No AI recommendations found")
    else:
        print(f"Error: {response.status_code}")
        print(response.text[:300])
        
except Exception as e:
    print(f"Exception: {e}")
