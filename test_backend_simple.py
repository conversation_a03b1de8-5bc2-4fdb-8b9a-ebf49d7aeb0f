#!/usr/bin/env python3
"""
Simple backend test without AI
"""

import subprocess
import time
import requests
import io
from PIL import Image
import numpy as np

def start_backend():
    """Start the backend server"""
    print("🚀 Starting backend server...")
    process = subprocess.Popen([
        "python", "-m", "uvicorn", "api.index:app", 
        "--reload", "--host", "0.0.0.0", "--port", "8000"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for server to start
    for i in range(10):
        try:
            response = requests.get("http://localhost:8000/ping", timeout=2)
            if response.status_code == 200:
                print("✅ Backend server started successfully")
                return process
        except:
            pass
        time.sleep(2)
    
    print("❌ Backend server failed to start")
    return None

def create_test_image():
    """Create a simple test image"""
    img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_endpoints_without_ai():
    """Test endpoints without AI to isolate the issue"""
    
    print("🧪 Testing Endpoints Without AI...")
    print("=" * 50)
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        return
    
    try:
        # Create test image
        test_image = create_test_image()
        
        # Test ping
        print("📡 Testing ping endpoint...")
        response = requests.get("http://localhost:8000/ping", timeout=5)
        print(f"✅ Ping: {response.status_code}")
        
        # Test flatfoot endpoint (should work even if AI fails)
        print("🦶 Testing flatfoot endpoint...")
        test_image.seek(0)
        files = {'file': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post(
            "http://localhost:8000/predict/yolo",
            files=files,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Flatfoot endpoint: Working")
            print(f"📊 Response keys: {list(result.keys())}")
            
            if 'ai_recommendations' in result:
                ai_rec = result['ai_recommendations']
                if ai_rec.get('success'):
                    print("✅ AI recommendations: Working")
                else:
                    print(f"⚠️  AI recommendations: {ai_rec.get('error', 'Failed')}")
            else:
                print("❌ AI recommendations: Missing from response")
        else:
            print(f"❌ Flatfoot endpoint: Error {response.status_code}")
            print(f"Response: {response.text[:200]}")
        
        print()
        
        # Test hallux valgus endpoint
        print("🦶 Testing hallux valgus endpoint...")
        test_image.seek(0)
        files = {'file': ('test.jpg', test_image, 'image/jpeg')}
        response = requests.post(
            "http://localhost:8000/predict/tensorflow",
            files=files,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Hallux valgus endpoint: Working")
            print(f"📊 Response keys: {list(result.keys())}")
            
            if 'ai_recommendations' in result:
                ai_rec = result['ai_recommendations']
                if ai_rec.get('success'):
                    print("✅ AI recommendations: Working")
                else:
                    print(f"⚠️  AI recommendations: {ai_rec.get('error', 'Failed')}")
            else:
                print("❌ AI recommendations: Missing from response")
        else:
            print(f"❌ Hallux valgus endpoint: Error {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    finally:
        # Clean up
        print("\n🧹 Cleaning up...")
        backend_process.terminate()
        backend_process.wait()
        print("✅ Backend stopped")

if __name__ == "__main__":
    test_endpoints_without_ai()
