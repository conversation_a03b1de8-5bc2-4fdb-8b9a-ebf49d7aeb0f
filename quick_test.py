import requests
import io
from PIL import Image
import numpy as np

# Create test image
img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
img = Image.fromarray(img_array)
img_bytes = io.BytesIO()
img.save(img_bytes, format='JPEG')
img_bytes.seek(0)

print("Testing flatfoot endpoint...")
files = {'file': ('test.jpg', img_bytes, 'image/jpeg')}
response = requests.post("http://localhost:8000/predict/yolo", files=files, timeout=30)

if response.status_code == 200:
    result = response.json()
    print("✅ Success!")
    print("Keys:", list(result.keys()))
    if 'ai_recommendations' in result:
        print("AI recommendations:", result['ai_recommendations'].get('success', False))
else:
    print("❌ Error:", response.status_code)
    print("Response:", response.text[:200])
