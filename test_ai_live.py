#!/usr/bin/env python3
"""
Live test for AI integration with actual OpenRouter API
"""
import requests
import json
import time

# Base URL for the FastAPI server
BASE_URL = "http://127.0.0.1:8000"

def test_ai_recommendations_live():
    """Test the AI recommendations endpoint with real API call"""
    
    print("🤖 Testing AI Recommendations with OpenRouter API")
    print("=" * 60)
    
    # Sample analysis results that would trigger AI recommendations
    test_data = {
        "analysis_results": {
            "yolo_predictions": [
                {
                    "label": "Flat Foot",
                    "confidence": 0.85,
                    "bbox": [100, 100, 200, 200]
                }
            ],
            "tensorflow_prediction": {
                "class": "Hallux Valgus",
                "confidence": 0.92
            }
        },
        "patient_info": {
            "age": 35,
            "weight": "70kg",
            "activity_level": "moderate",
            "symptoms": "foot pain after walking, especially in the evening"
        }
    }
    
    try:
        print("📤 Sending request to AI recommendations endpoint...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/ai-recommendations",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30  # 30 second timeout
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️  Response time: {response_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI recommendations endpoint working!")
            print("\n📋 Response Summary:")
            print(f"   Success: {result.get('success', 'Unknown')}")
            print(f"   Model Used: {result.get('model_used', 'Unknown')}")
            
            if result.get('analysis_summary'):
                summary = result['analysis_summary']
                print(f"   Flatfoot Detected: {summary.get('flatfoot_detected', 'Unknown')}")
                print(f"   Hallux Valgus Detected: {summary.get('hallux_valgus_detected', 'Unknown')}")
            
            print("\n🩺 AI Recommendations Preview:")
            recommendations = result.get('recommendations', 'No recommendations available')
            # Show first 300 characters of recommendations
            preview = recommendations[:300] + "..." if len(recommendations) > 300 else recommendations
            print(preview)
            
            # Save full recommendations to file for review
            with open('ai_recommendations_output.txt', 'w', encoding='utf-8') as f:
                f.write("AI Recommendations Test Output\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Test Data:\n{json.dumps(test_data, indent=2)}\n\n")
                f.write(f"Full Response:\n{json.dumps(result, indent=2)}\n\n")
                f.write("Full Recommendations:\n")
                f.write(recommendations)
            
            print(f"\n💾 Full recommendations saved to: ai_recommendations_output.txt")
            
        else:
            print("❌ AI recommendations endpoint failed")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - AI service may be slow")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - make sure FastAPI server is running")
        print("Run: npm run fastapi-dev")
    except Exception as e:
        print(f"❌ Error testing AI recommendations: {e}")

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/ping", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_fallback_system():
    """Test the fallback system by simulating API failure"""
    print("\n🛡️  Testing Fallback System")
    print("=" * 30)
    
    # This test would require temporarily breaking the API key
    # For now, we'll just verify the endpoint exists
    test_data = {
        "analysis_results": {
            "yolo_predictions": [
                {
                    "label": "Normal",
                    "confidence": 0.95,
                    "bbox": [100, 100, 200, 200]
                }
            ],
            "tensorflow_prediction": {
                "class": "Normal",
                "confidence": 0.93
            }
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/ai-recommendations",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ AI service working - fallback not needed")
            else:
                print("✅ Fallback system activated")
                print(f"   Fallback recommendations provided: {bool(result.get('fallback_recommendations'))}")
        
    except Exception as e:
        print(f"⚠️  Could not test fallback system: {e}")

if __name__ == "__main__":
    print("🧪 Live AI Integration Test")
    print("=" * 50)
    
    # Test health check first
    if test_health_check():
        print("\n🤖 Testing AI Integration with OpenRouter...")
        test_ai_recommendations_live()
        test_fallback_system()
    else:
        print("❌ Server is not responding. Make sure FastAPI server is running.")
        print("Run: npm run fastapi-dev")
    
    print("\n" + "=" * 50)
    print("🎉 Live test completed!")
    print("\n💡 Next steps:")
    print("1. Check ai_recommendations_output.txt for full AI response")
    print("2. Test the /predict/combined endpoint with an actual image")
    print("3. Integrate the frontend component for full user experience")
