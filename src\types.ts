export type ModelType = 'hallux_valgus' | 'flat_foot' | 'combined';

export interface AnalysisResult {
  footType: 'Left' | 'Right';
  condition: string;
  confidence: number;
  date: string;
  annotatedImageUrl?: string;
}

export interface YoloPrediction {
  label: string;
  confidence: number;
  bbox: number[];
}

export interface AIRecommendation {
  success: boolean;
  recommendations?: string;
  fallback_recommendations?: string;
  error?: string;
}

export interface YoloResponse {
  predictions: YoloPrediction[];
  annotated_image_url: string;
  ai_recommendations?: AIRecommendation;
  analysis_type?: string;
  timestamp?: string;
}

export interface TensorflowResponse {
  class: string;
  confidence: number;
  ai_recommendations?: AIRecommendation;
  analysis_type?: string;
  timestamp?: string;
}

export interface NavItem {
  label: string;
  href: string;
  icon: string;
}