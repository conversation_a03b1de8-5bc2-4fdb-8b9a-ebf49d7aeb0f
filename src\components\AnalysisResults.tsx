import React from 'react';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  Target,
  TrendingUp,
  Clock,
  MapPin,
  BarChart3,
  Eye,
  Zap
} from 'lucide-react';

interface AnalysisData {
  condition: string;
  confidence: number;
  severity?: string;
  affected_areas?: string[];
  angle_measurement?: string;
  risk_level?: string;
  additional_info?: any;
}

interface AnalysisResultsProps {
  results: {
    flatfoot_analysis?: AnalysisData;
    hallux_valgus_analysis?: AnalysisData;
    combined_risk_assessment?: string;
    analysis_timestamp?: string;
    image_filename?: string;
    annotated_image_url?: string;
  };
  selectedImage?: string;
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({ results, selectedImage }) => {
  // Helper function to get confidence color
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'green';
    if (confidence >= 0.6) return 'yellow';
    return 'red';
  };

  // Helper function to get severity color
  const getSeverityColor = (severity?: string) => {
    if (!severity) return 'gray';
    const sev = severity.toLowerCase();
    if (sev.includes('severe')) return 'red';
    if (sev.includes('moderate')) return 'yellow';
    if (sev.includes('mild')) return 'blue';
    return 'green';
  };

  // Helper function to get risk level color
  const getRiskColor = (risk?: string) => {
    if (!risk) return 'gray';
    const r = risk.toLowerCase();
    if (r.includes('high')) return 'red';
    if (r.includes('moderate')) return 'yellow';
    if (r.includes('low')) return 'green';
    return 'blue';
  };

  // Render individual condition analysis
  const renderConditionAnalysis = (
    title: string,
    analysis: AnalysisData | undefined,
    icon: React.ComponentType<any>,
    primaryColor: string
  ) => {
    if (!analysis) {
      return (
        <div className={`bg-gray-50 border border-gray-200 rounded-lg p-6`}>
          <div className="flex items-center mb-4">
            {React.createElement(icon, { className: `h-6 w-6 text-gray-400 mr-3` })}
            <h3 className="text-xl font-bold text-gray-600">{title}</h3>
          </div>
          <div className="text-center py-8">
            <Info className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No analysis data available</p>
          </div>
        </div>
      );
    }

    const confidenceColor = getConfidenceColor(analysis.confidence);
    const severityColor = getSeverityColor(analysis.severity);

    return (
      <div className={`bg-${primaryColor}-50 border border-${primaryColor}-200 rounded-lg p-6`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            {React.createElement(icon, { className: `h-6 w-6 text-${primaryColor}-600 mr-3` })}
            <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          </div>
          <div className="text-right">
            <span className={`px-3 py-1 rounded-full text-sm font-medium bg-${confidenceColor}-100 text-${confidenceColor}-800`}>
              {(analysis.confidence * 100).toFixed(1)}% Confidence
            </span>
          </div>
        </div>

        {/* Main Condition Status */}
        <div className="mb-6">
          <div className="flex items-center mb-3">
            <Target className={`h-5 w-5 text-${primaryColor}-600 mr-2`} />
            <span className="font-semibold text-gray-900">Condition Status:</span>
          </div>
          <div className={`bg-white border border-${primaryColor}-200 rounded-lg p-4`}>
            <p className="text-lg font-medium text-gray-900">{analysis.condition}</p>
            {analysis.severity && (
              <div className="mt-2 flex items-center">
                <AlertTriangle className={`h-4 w-4 text-${severityColor}-600 mr-2`} />
                <span className={`px-2 py-1 rounded text-sm font-medium bg-${severityColor}-100 text-${severityColor}-800`}>
                  {analysis.severity}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Confidence Score */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <BarChart3 className="h-4 w-4 text-gray-600 mr-2" />
              <span className="font-medium text-gray-900">Confidence Score</span>
            </div>
            <div className="flex items-center">
              <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                <div 
                  className={`bg-${confidenceColor}-500 h-2 rounded-full transition-all duration-300`}
                  style={{ width: `${analysis.confidence * 100}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-700">
                {(analysis.confidence * 100).toFixed(1)}%
              </span>
            </div>
          </div>

          {/* Additional Measurements */}
          {analysis.angle_measurement && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <TrendingUp className="h-4 w-4 text-gray-600 mr-2" />
                <span className="font-medium text-gray-900">Angle Measurement</span>
              </div>
              <p className="text-lg font-semibold text-gray-900">{analysis.angle_measurement}</p>
            </div>
          )}
        </div>

        {/* Affected Areas */}
        {analysis.affected_areas && analysis.affected_areas.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <MapPin className={`h-5 w-5 text-${primaryColor}-600 mr-2`} />
              <span className="font-semibold text-gray-900">Affected Areas:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {analysis.affected_areas.map((area, index) => (
                <span 
                  key={index}
                  className={`px-3 py-1 bg-${primaryColor}-100 text-${primaryColor}-800 rounded-full text-sm font-medium`}
                >
                  {area}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Risk Level */}
        {analysis.risk_level && (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <Zap className={`h-5 w-5 text-${primaryColor}-600 mr-2`} />
              <span className="font-semibold text-gray-900">Risk Level:</span>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium bg-${getRiskColor(analysis.risk_level)}-100 text-${getRiskColor(analysis.risk_level)}-800`}>
              {analysis.risk_level}
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header with Analysis Info */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Analysis Results</h2>
            {results.analysis_timestamp && (
              <div className="flex items-center text-blue-100">
                <Clock className="h-4 w-4 mr-2" />
                <span className="text-sm">
                  Analyzed: {new Date(results.analysis_timestamp).toLocaleString()}
                </span>
              </div>
            )}
          </div>
          {results.image_filename && (
            <div className="text-right">
              <div className="flex items-center text-blue-100 mb-1">
                <Eye className="h-4 w-4 mr-2" />
                <span className="text-sm">Image: {results.image_filename}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Image Display */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Original Image */}
        {selectedImage && (
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Original Image
            </h3>
            <img 
              src={selectedImage} 
              alt="Original analysis" 
              className="w-full h-auto rounded-lg border border-gray-300"
            />
          </div>
        )}

        {/* Annotated Image */}
        {results.annotated_image_url && (
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Analysis Overlay
            </h3>
            <img 
              src={results.annotated_image_url} 
              alt="Annotated analysis" 
              className="w-full h-auto rounded-lg border border-gray-300"
            />
          </div>
        )}
      </div>

      {/* Individual Condition Analyses */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Flatfoot Analysis */}
        {renderConditionAnalysis(
          "Flatfoot Analysis",
          results.flatfoot_analysis,
          Activity,
          "green"
        )}

        {/* Hallux Valgus Analysis */}
        {renderConditionAnalysis(
          "Hallux Valgus Analysis", 
          results.hallux_valgus_analysis,
          TrendingUp,
          "blue"
        )}
      </div>

      {/* Combined Risk Assessment */}
      {results.combined_risk_assessment && (
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-6 w-6 text-purple-600 mr-3" />
            <h3 className="text-xl font-bold text-gray-900">Combined Risk Assessment</h3>
          </div>
          
          <div className="bg-white border border-purple-200 rounded-lg p-4">
            <p className="text-lg text-gray-900 leading-relaxed">
              {results.combined_risk_assessment}
            </p>
          </div>

          <div className="mt-4 p-3 bg-purple-100 border border-purple-200 rounded-lg">
            <p className="text-purple-800 text-sm">
              <strong>Note:</strong> This assessment considers the interaction between both conditions. 
              Individual condition analyses are shown above for detailed insights.
            </p>
          </div>
        </div>
      )}

      {/* Summary Statistics */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          Analysis Summary
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {results.flatfoot_analysis ? '✓' : '○'}
            </div>
            <div className="text-sm text-gray-600">Flatfoot Analyzed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {results.hallux_valgus_analysis ? '✓' : '○'}
            </div>
            <div className="text-sm text-gray-600">Hallux Valgus Analyzed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {results.combined_risk_assessment ? '✓' : '○'}
            </div>
            <div className="text-sm text-gray-600">Risk Assessment</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {results.annotated_image_url ? '✓' : '○'}
            </div>
            <div className="text-sm text-gray-600">Visual Analysis</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisResults;
