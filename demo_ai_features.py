#!/usr/bin/env python3
"""
Comprehensive demo of AI features
"""
import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000"

def demo_scenario(scenario_name, test_data, description):
    """Demo a specific medical scenario"""
    
    print(f"\n🏥 {scenario_name}")
    print("=" * 60)
    print(f"📝 Scenario: {description}")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/ai-recommendations",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
            print(f"🤖 Model: {result.get('model_used', 'Unknown')}")
            print(f"✅ Success: {result.get('success', False)}")
            
            # Show analysis summary
            if result.get('analysis_summary'):
                summary = result['analysis_summary']
                print(f"📊 Analysis Summary:")
                print(f"   • Flatfoot detected: {summary.get('flatfoot_detected', 'Unknown')}")
                print(f"   • Hallux valgus detected: {summary.get('hallux_valgus_detected', 'Unknown')}")
            
            # Show AI recommendations preview
            recommendations = result.get('recommendations', '')
            if recommendations:
                print(f"\n🩺 AI Recommendations Preview:")
                # Extract key sections
                lines = recommendations.split('\n')
                for line in lines[:10]:  # Show first 10 lines
                    if line.strip():
                        print(f"   {line}")
                if len(lines) > 10:
                    print("   ...")
                    
            return True
        else:
            print(f"❌ Failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run comprehensive AI demo"""
    
    print("🤖 AI-Powered Foot Deformity Analysis - Feature Demo")
    print("=" * 70)
    
    # Check server health
    try:
        response = requests.get(f"{BASE_URL}/ping", timeout=5)
        if response.status_code != 200:
            print("❌ Server not responding properly")
            return
        print("✅ FastAPI server is running")
    except:
        print("❌ Cannot connect to server. Run: npm run fastapi-dev")
        return
    
    # Demo scenarios
    scenarios = [
        {
            "name": "Scenario 1: Normal Healthy Feet",
            "description": "Young adult with no foot deformities",
            "data": {
                "analysis_results": {
                    "yolo_predictions": [
                        {"label": "Normal", "confidence": 0.95, "bbox": [100, 100, 200, 200]}
                    ],
                    "tensorflow_prediction": {
                        "class": "Normal", "confidence": 0.93
                    }
                },
                "patient_info": {
                    "age": 25,
                    "activity_level": "high",
                    "symptoms": "none"
                }
            }
        },
        {
            "name": "Scenario 2: Mild Flatfoot",
            "description": "Middle-aged person with mild flatfoot condition",
            "data": {
                "analysis_results": {
                    "yolo_predictions": [
                        {"label": "Flat Foot", "confidence": 0.72, "bbox": [100, 100, 200, 200]}
                    ],
                    "tensorflow_prediction": {
                        "class": "Normal", "confidence": 0.88
                    }
                },
                "patient_info": {
                    "age": 45,
                    "weight": "80kg",
                    "activity_level": "moderate",
                    "symptoms": "mild foot fatigue after long walks"
                }
            }
        },
        {
            "name": "Scenario 3: Severe Hallux Valgus",
            "description": "Elderly patient with significant bunion formation",
            "data": {
                "analysis_results": {
                    "yolo_predictions": [
                        {"label": "Normal", "confidence": 0.65, "bbox": [100, 100, 200, 200]}
                    ],
                    "tensorflow_prediction": {
                        "class": "Hallux Valgus", "confidence": 0.94
                    }
                },
                "patient_info": {
                    "age": 68,
                    "weight": "75kg",
                    "activity_level": "low",
                    "symptoms": "severe pain when wearing shoes, difficulty walking"
                }
            }
        },
        {
            "name": "Scenario 4: Combined Deformities",
            "description": "Complex case with both flatfoot and hallux valgus",
            "data": {
                "analysis_results": {
                    "yolo_predictions": [
                        {"label": "Flat Foot", "confidence": 0.89, "bbox": [100, 100, 200, 200]}
                    ],
                    "tensorflow_prediction": {
                        "class": "Hallux Valgus", "confidence": 0.91
                    }
                },
                "patient_info": {
                    "age": 55,
                    "weight": "85kg",
                    "activity_level": "moderate",
                    "symptoms": "foot pain, difficulty finding comfortable shoes, balance issues"
                }
            }
        }
    ]
    
    # Run all scenarios
    successful_tests = 0
    for scenario in scenarios:
        success = demo_scenario(
            scenario["name"], 
            scenario["data"], 
            scenario["description"]
        )
        if success:
            successful_tests += 1
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n🎯 Demo Summary")
    print("=" * 30)
    print(f"✅ Successful tests: {successful_tests}/{len(scenarios)}")
    print(f"🤖 AI Model: DeepSeek R1 (Free)")
    print(f"🔗 API: OpenRouter")
    
    if successful_tests == len(scenarios):
        print("\n🎉 All AI features are working perfectly!")
        print("\n💡 Next Steps:")
        print("1. 🖼️  Test with real foot images using /predict/combined endpoint")
        print("2. 🎨 Integrate the React frontend component")
        print("3. 👥 Train your team on the new AI recommendations feature")
        print("4. 📊 Monitor usage and gather feedback from medical professionals")
    else:
        print(f"\n⚠️  {len(scenarios) - successful_tests} tests failed")
        print("Check the error messages above for troubleshooting")

if __name__ == "__main__":
    main()
