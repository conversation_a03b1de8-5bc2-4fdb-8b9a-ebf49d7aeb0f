from fastapi import Fast<PERSON><PERSON>, File, UploadFile
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from ultralytics import YOL<PERSON>
from PIL import Image
import os
import numpy as np
import tensorflow as tf
from io import BytesIO
import uvicorn
from tensorflow.keras.models import load_model
import httpx
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables from .env file
load_dotenv()
# Initialize FastAPI app
app = FastAPI()

# OpenRouter API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
DEEPSEEK_MODEL = "deepseek/deepseek-r1-0528:free"

# Pydantic models for request/response
class AnalysisResult(BaseModel):
    yolo_predictions: Optional[list] = None
    tensorflow_prediction: Optional[Dict[str, Any]] = None

class AIRecommendationRequest(BaseModel):
    analysis_results: AnalysisResult
    patient_info: Optional[Dict[str, Any]] = None

# Enable CORS for local development
origins = [
    "http://localhost",
    "http://localhost:3000",
     "http://localhost:5173",  # Added Vite's default port
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Directory to save annotated images
OUTPUT_DIR = "annotated_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Load YOLO model
yolo_model = YOLO(r'Models\flatfoot_detection_yolov8.pt')

# Load TensorFlow model with error handling
try:
    tensorflow_model = tf.keras.models.load_model(r'Models\hallux_valgus_model12.keras', compile=False)
    print("TensorFlow model loaded successfully")
except Exception as e:
    print(f"Error loading TensorFlow model: {e}")
    # Try alternative loading methods
    try:
        tensorflow_model = tf.keras.models.load_model(r'Models\hallux_valgus_model12.keras',
                                                     custom_objects=None,
                                                     compile=False,
                                                     safe_mode=False)
        print("TensorFlow model loaded with alternative method")
    except Exception as e2:
        print(f"Failed to load TensorFlow model with alternative method: {e2}")
        tensorflow_model = None


# Custom labels for YOLO
CUSTOM_LABELS = {0: "Flat Foot", 1: "Normal"}
CLASS_NAMES = ["Hallux Valgus", "Normal"]

@app.get("/ping")
async def ping():
    """
    Health check endpoint.
    """
    return {"message": "API is alive!"}

def read_file_as_image(data: bytes) -> np.ndarray:
    """
    Convert uploaded image file bytes to NumPy array.
    """
    image = np.array(Image.open(BytesIO(data)))
    return image

async def get_individual_ai_recommendations(analysis_results: AnalysisResult, analysis_type: str) -> Dict[str, Any]:
    """
    Get precise AI recommendations for individual analysis (flatfoot OR hallux valgus only).
    """
    try:
        # Prepare specialized prompt for individual analysis
        if analysis_type == "flatfoot":
            detections = analysis_results.yolo_predictions
            total_detections = len(detections)

            prompt = f"""
You are a specialized podiatric AI assistant focusing exclusively on FLATFOOT analysis. Analyze the following detection results and provide precise, actionable recommendations.

FLATFOOT DETECTION RESULTS:
"""
            if total_detections > 0:
                for pred in detections:
                    prompt += f"- Detection: {pred['label']} (Confidence: {pred['confidence']:.2f})\n"
                highest_confidence = max(pred['confidence'] for pred in detections)
            else:
                prompt += "- No flatfoot detections found (Normal arch detected)\n"
                highest_confidence = 0.0

            prompt += f"""
Total detections: {total_detections}
Analysis Status: {'Flatfoot detected' if total_detections > 0 else 'Normal foot arch'}

Provide PRECISE recommendations in this exact format:

## FLATFOOT ANALYSIS SUMMARY
Based on the detection results ({total_detections} detections), provide specific interpretation.

## SEVERITY CLASSIFICATION
- **Detected Level**: {'[Mild/Moderate/Severe Flatfoot based on detections]' if total_detections > 0 else '[Normal Arch - No flatfoot detected]'}
- **Confidence Assessment**: Analysis based on detection confidence {highest_confidence:.2f}
- **Arch Status**: {'Arch collapse detected' if total_detections > 0 else 'Normal arch structure maintained'}

## IMMEDIATE RECOMMENDATIONS (Next 1-2 weeks)
- Specific footwear modifications for flatfoot
- Immediate pain relief strategies if applicable
- Activity restrictions or modifications

## TARGETED TREATMENT PLAN
### Arch Support Strategy
- Specific orthotic recommendations for flatfoot
- Custom vs. over-the-counter options
- Proper fitting guidelines

### Strengthening Exercises (Flatfoot-specific)
- Arch strengthening exercises with repetitions
- Calf muscle strengthening routine
- Toe exercises for arch support

### Biomechanical Corrections
- Gait modification techniques
- Posture adjustments for flatfoot
- Weight distribution strategies

## PROGRESSION MONITORING
- Key indicators to track improvement
- Warning signs of worsening flatfoot
- Timeline for expected improvements

## PROFESSIONAL CONSULTATION CRITERIA
- When to see a podiatrist specifically for flatfoot
- Surgical intervention considerations
- Advanced imaging needs

Be specific to FLATFOOT condition only. Use precise medical terminology with clear explanations.
"""

        elif analysis_type == "hallux_valgus":
            pred = analysis_results.tensorflow_prediction
            if not pred:
                pred = {"class": "Normal", "confidence": 0.0}
            prompt = f"""
You are a specialized podiatric AI assistant focusing exclusively on HALLUX VALGUS (bunion) analysis. Analyze the following classification results and provide precise, actionable recommendations.

HALLUX VALGUS CLASSIFICATION RESULTS:
- Classification: {pred['class']}
- Confidence: {pred['confidence']:.2f}

Provide PRECISE recommendations in this exact format:

## HALLUX VALGUS ANALYSIS SUMMARY
Based on the {pred['class']} classification with {pred['confidence']:.2f} confidence, provide specific interpretation.

## SEVERITY CLASSIFICATION
- **Bunion Grade**: [Normal/Mild Bunion/Moderate Bunion/Severe Bunion]
- **Confidence Assessment**: Analysis of the {pred['confidence']:.2f} confidence score
- **Deformity Angle**: Estimated hallux valgus angle range

## IMMEDIATE RECOMMENDATIONS (Next 1-2 weeks)
- Specific footwear modifications for bunions
- Immediate pain relief strategies for big toe joint
- Activity modifications to reduce bunion pressure

## TARGETED TREATMENT PLAN
### Bunion-Specific Interventions
- Toe spacers and bunion pads recommendations
- Proper shoe fitting for hallux valgus
- Night splint considerations

### Strengthening Exercises (Hallux Valgus-specific)
- Big toe strengthening exercises
- Foot intrinsic muscle exercises
- Flexibility routines for hallux valgus

### Biomechanical Corrections
- Gait modifications for bunion relief
- Weight-bearing adjustments
- Toe alignment strategies

## PROGRESSION MONITORING
- Key indicators to track bunion progression
- Warning signs of worsening hallux valgus
- Timeline for expected improvements

## PROFESSIONAL CONSULTATION CRITERIA
- When to see a podiatrist specifically for bunions
- Surgical intervention considerations for hallux valgus
- Advanced imaging needs for bunion assessment

Be specific to HALLUX VALGUS condition only. Use precise medical terminology with clear explanations.
"""
        else:
            return {
                "success": False,
                "error": f"Invalid analysis type: {analysis_type}. Expected 'flatfoot' or 'hallux_valgus'",
                "fallback_recommendations": "Please ensure proper analysis data is provided."
            }

        # Make API call to OpenRouter
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": DEEPSEEK_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": f"You are a specialized podiatric AI assistant focusing exclusively on {analysis_type.upper()} analysis. Provide evidence-based, condition-specific recommendations."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1200,
            "temperature": 0.6
        }

        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.post(
                f"{OPENROUTER_BASE_URL}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "recommendations": ai_response,
                    "model_used": DEEPSEEK_MODEL,
                    "analysis_type": analysis_type,
                    "analysis_summary": {
                        "condition_detected": analysis_type,
                        "confidence_level": (max(pred['confidence'] for pred in analysis_results.yolo_predictions) if analysis_results.yolo_predictions else 0.0) if analysis_type == "flatfoot" else analysis_results.tensorflow_prediction['confidence'],
                        "specific_findings": len(analysis_results.yolo_predictions) if analysis_type == "flatfoot" else analysis_results.tensorflow_prediction['class']
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"OpenRouter API error: {response.status_code} - {response.text}",
                    "fallback_recommendations": get_individual_fallback_recommendations(analysis_results, analysis_type)
                }

    except Exception as e:
        return {
            "success": False,
            "error": f"Individual AI recommendation service error: {str(e)}",
            "fallback_recommendations": get_individual_fallback_recommendations(analysis_results, analysis_type)
        }

async def get_ai_recommendations(analysis_results: AnalysisResult, patient_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get AI-powered recommendations based on foot deformity analysis results.
    """
    try:
        # Prepare the prompt for the AI agent
        prompt = f"""
You are a medical AI assistant specializing in foot deformity analysis. Based on the following analysis results, provide comprehensive recommendations and suggestions.

Analysis Results:
"""

        # Add YOLO (Flatfoot) results
        if analysis_results.yolo_predictions:
            prompt += f"\nFlatfoot Detection (YOLO):\n"
            for pred in analysis_results.yolo_predictions:
                prompt += f"- {pred['label']} (Confidence: {pred['confidence']:.2f})\n"

        # Add TensorFlow (Hallux Valgus) results
        if analysis_results.tensorflow_prediction:
            prompt += f"\nHallux Valgus Detection (TensorFlow):\n"
            prompt += f"- {analysis_results.tensorflow_prediction['class']} (Confidence: {analysis_results.tensorflow_prediction['confidence']:.2f})\n"

        # Add patient information if provided
        if patient_info:
            prompt += f"\nPatient Information:\n"
            for key, value in patient_info.items():
                prompt += f"- {key}: {value}\n"

        prompt += """
Please provide a comprehensive medical analysis in the following structured format:

## DIAGNOSIS SUMMARY
Provide a clear, patient-friendly interpretation of the analysis results.

## SEVERITY ASSESSMENT
Rate the severity level and explain the classification:
- **Severity Level**: [Mild/Moderate/Severe/Normal]
- **Risk Factors**: List specific risk factors identified
- **Progression Likelihood**: Assess potential for condition progression

## TREATMENT RECOMMENDATIONS

### Immediate Actions (0-2 weeks)
- List urgent steps to take immediately
- Pain management if applicable
- Activity modifications

### Conservative Treatment (2-12 weeks)
- **Exercises**: Specific therapeutic exercises with frequency
- **Orthotics**: Recommended supportive devices
- **Physical Therapy**: When and what type needed
- **Lifestyle Changes**: Daily habit modifications

### Medical Intervention Criteria
- When to consult a podiatrist or orthopedic specialist
- Signs that indicate need for advanced treatment
- Potential surgical considerations if applicable

## PREVENTION STRATEGIES
- **Footwear Guidelines**: Specific shoe recommendations
- **Daily Habits**: Preventive activities and postures
- **Exercise Routine**: Strengthening and flexibility exercises
- **Weight Management**: If relevant to condition

## FOLLOW-UP TIMELINE
- **Short-term**: 2-4 week check-in recommendations
- **Medium-term**: 3-6 month monitoring
- **Long-term**: Annual assessment needs
- **Warning Signs**: Symptoms requiring immediate medical attention

## LIFESTYLE MODIFICATIONS
- **Work Environment**: Workplace adjustments
- **Sports/Activities**: Safe participation guidelines
- **Home Care**: Daily self-care routines

## PATIENT EDUCATION
- **Understanding Your Condition**: Key points about the diagnosis
- **Myth Busting**: Common misconceptions to avoid
- **Resources**: Helpful educational materials or websites

Format each section clearly with bullet points, numbered lists, and bold headings. Use simple medical terminology with explanations when needed. Be encouraging while emphasizing the importance of professional medical consultation.
"""

        # Make API call to OpenRouter
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": DEEPSEEK_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a knowledgeable medical AI assistant specializing in podiatry and foot deformities. Provide evidence-based recommendations while emphasizing the importance of professional medical consultation."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1500,
            "temperature": 0.7
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{OPENROUTER_BASE_URL}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "recommendations": ai_response,
                    "model_used": DEEPSEEK_MODEL,
                    "analysis_summary": {
                        "flatfoot_detected": any(pred["label"] == "Flat Foot" for pred in (analysis_results.yolo_predictions or [])),
                        "hallux_valgus_detected": analysis_results.tensorflow_prediction and analysis_results.tensorflow_prediction.get("class") == "Hallux Valgus"
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"OpenRouter API error: {response.status_code} - {response.text}",
                    "fallback_recommendations": get_fallback_recommendations(analysis_results)
                }

    except Exception as e:
        return {
            "success": False,
            "error": f"AI recommendation service error: {str(e)}",
            "fallback_recommendations": get_fallback_recommendations(analysis_results)
        }

def get_enhanced_individual_recommendations(analysis_results: AnalysisResult, analysis_type: str) -> Dict[str, Any]:
    """
    Provide enhanced, precise recommendations for individual analysis when AI service is unavailable.
    """
    if analysis_type == "flatfoot":
        detections = analysis_results.yolo_predictions or []
        total_detections = len(detections)

        if total_detections > 0:
            highest_confidence = max(pred['confidence'] for pred in detections)
            severity = "Severe" if highest_confidence > 0.8 else "Moderate" if highest_confidence > 0.6 else "Mild"

            recommendations = f"""## 🦶 FLATFOOT ANALYSIS - PRECISE RECOMMENDATIONS

### 📊 DETECTION SUMMARY
- **Flatfoot Detections**: {total_detections} finding(s)
- **Highest Confidence**: {highest_confidence:.2f} ({highest_confidence*100:.1f}%)
- **Severity Assessment**: {severity} Flatfoot

### 🎯 IMMEDIATE ACTIONS (Next 1-2 weeks)
- **Footwear**: Switch to shoes with strong arch support immediately
- **Activity**: Limit prolonged standing on hard surfaces
- **Pain Management**: Apply ice for 15-20 minutes if experiencing discomfort
- **Support**: Use over-the-counter arch support insoles as temporary relief

### 💪 TARGETED TREATMENT PLAN

#### Arch Strengthening Exercises (Daily)
- **Calf Raises**: 3 sets of 15 repetitions, hold for 3 seconds
- **Toe Curls**: Pick up marbles/towels with toes, 2 sets of 10
- **Arch Lifts**: Lift arch while keeping heel and toes on ground, hold 5 seconds
- **Tennis Ball Roll**: Roll foot over tennis ball for 5 minutes

#### Biomechanical Corrections
- **Gait Training**: Focus on proper heel-to-toe walking pattern
- **Weight Distribution**: Practice standing with weight evenly distributed
- **Posture**: Maintain proper alignment to reduce foot stress

### 🔍 MONITORING & PROGRESSION
- **Track Progress**: Monitor pain levels and arch height weekly
- **Warning Signs**: Increasing pain, swelling, or difficulty walking
- **Timeline**: Expect gradual improvement over 6-12 weeks with consistent exercises

### 👨‍⚕️ PROFESSIONAL CONSULTATION NEEDED IF:
- Pain persists or worsens after 2 weeks of conservative treatment
- Difficulty finding comfortable footwear despite arch supports
- Numbness, tingling, or severe swelling develops
- Confidence level > 0.7 (indicating significant flatfoot deformity)

### 📚 SPECIALIZED RECOMMENDATIONS
- **Custom Orthotics**: Consider professional fitting if over-the-counter supports insufficient
- **Physical Therapy**: May be beneficial for severe cases (confidence > 0.8)
- **Surgical Consultation**: Rarely needed, only for severe, symptomatic cases

**Medical Disclaimer**: This analysis is for informational purposes only. Consult a podiatrist or orthopedic specialist for comprehensive evaluation and personalized treatment planning."""

        else:
            recommendations = f"""## 🦶 FLATFOOT ANALYSIS - NORMAL ARCH DETECTED

### 📊 DETECTION SUMMARY
- **Result**: No flatfoot detected - Normal arch structure
- **Arch Status**: Healthy arch height maintained
- **Assessment**: Normal foot biomechanics

### ✅ PREVENTIVE RECOMMENDATIONS

#### Maintain Healthy Feet
- **Footwear**: Continue wearing supportive shoes with good arch support
- **Exercise**: Regular foot strengthening exercises to maintain arch integrity
- **Monitoring**: Annual foot health check-ups

#### Strengthening Routine (3x/week)
- **Calf Raises**: 2 sets of 12 to maintain posterior tibial strength
- **Toe Exercises**: Flex and extend toes to maintain intrinsic muscle strength
- **Balance Training**: Single-leg stands to improve proprioception

### 🛡️ PREVENTION STRATEGIES
- **Weight Management**: Maintain healthy BMI to reduce foot stress
- **Activity Modification**: Avoid excessive high-impact activities on hard surfaces
- **Proper Footwear**: Replace worn shoes regularly, avoid flat shoes without support

### 📋 FOLLOW-UP
- **Routine Monitoring**: Annual foot assessment recommended
- **Watch For**: Any changes in arch height or foot pain
- **Professional Care**: Consult podiatrist if foot pain or structural changes develop

**Excellent News**: Your foot arch structure appears normal. Continue current foot care practices to maintain healthy feet."""

        return {
            "success": True,
            "recommendations": recommendations,
            "analysis_type": "flatfoot",
            "model_used": "Enhanced Clinical Algorithm",
            "analysis_summary": {
                "condition_detected": "flatfoot" if total_detections > 0 else "normal_arch",
                "confidence_level": highest_confidence if total_detections > 0 else 0.0,
                "specific_findings": f"{total_detections} detections" if total_detections > 0 else "normal arch"
            }
        }

    elif analysis_type == "hallux_valgus":
        pred = analysis_results.tensorflow_prediction or {"class": "Normal", "confidence": 0.0}
        classification = pred['class']
        confidence = pred['confidence']

        if classification == "Hallux Valgus" or confidence > 0.5:
            severity = "Severe" if confidence > 0.8 else "Moderate" if confidence > 0.6 else "Mild"

            recommendations = f"""## 🦶 HALLUX VALGUS ANALYSIS - PRECISE RECOMMENDATIONS

### 📊 DETECTION SUMMARY
- **Classification**: {classification}
- **Confidence**: {confidence:.2f} ({confidence*100:.1f}%)
- **Severity Assessment**: {severity} Hallux Valgus (Bunion)

### 🎯 IMMEDIATE ACTIONS (Next 1-2 weeks)
- **Footwear**: Switch to wide-toe box shoes immediately
- **Avoid**: High heels, pointed shoes, tight-fitting footwear
- **Pain Relief**: Apply ice for 15-20 minutes if experiencing pain
- **Protection**: Use bunion pads or toe spacers for immediate comfort

### 💪 TARGETED TREATMENT PLAN

#### Bunion-Specific Exercises (Daily)
- **Toe Stretches**: Spread toes apart and hold for 10 seconds, repeat 10 times
- **Big Toe Flexion**: Move big toe up and down, 3 sets of 15
- **Resistance Band**: Pull big toe toward midline against resistance
- **Marble Pickup**: Strengthen intrinsic foot muscles, 2 sets of 10

#### Biomechanical Corrections
- **Gait Modification**: Focus on proper toe-off phase during walking
- **Weight Distribution**: Practice even weight bearing across forefoot
- **Toe Alignment**: Use toe spacers during rest periods

### 🔍 MONITORING & PROGRESSION
- **Track Progress**: Monitor bunion size and pain levels weekly
- **Warning Signs**: Increasing deformity, severe pain, or difficulty walking
- **Timeline**: Conservative treatment effective in 60-70% of mild-moderate cases

### 👨‍⚕️ PROFESSIONAL CONSULTATION NEEDED IF:
- Pain significantly impacts daily activities
- Progressive deformity despite conservative treatment
- Confidence level > 0.7 (indicating significant bunion deformity)
- Development of secondary toe deformities

### 📚 SPECIALIZED RECOMMENDATIONS
- **Custom Orthotics**: Recommended for moderate-severe cases (confidence > 0.6)
- **Night Splints**: May help prevent progression in early stages
- **Surgical Consultation**: Consider if conservative treatment fails after 6 months

**Medical Disclaimer**: This analysis is for informational purposes only. Consult a podiatrist or orthopedic specialist for comprehensive evaluation and personalized treatment planning."""

        else:
            recommendations = f"""## 🦶 HALLUX VALGUS ANALYSIS - NORMAL TOE ALIGNMENT

### 📊 DETECTION SUMMARY
- **Result**: No hallux valgus detected - Normal big toe alignment
- **Classification**: {classification}
- **Assessment**: Healthy toe structure and alignment

### ✅ PREVENTIVE RECOMMENDATIONS

#### Maintain Healthy Toe Alignment
- **Footwear**: Continue wearing properly fitted shoes with adequate toe room
- **Exercise**: Regular toe flexibility and strengthening exercises
- **Monitoring**: Annual foot health assessments

#### Prevention Routine (3x/week)
- **Toe Stretches**: Maintain flexibility and prevent stiffness
- **Strengthening**: Big toe exercises to maintain muscle balance
- **Flexibility**: Calf and Achilles stretches to reduce forefoot pressure

### 🛡️ PREVENTION STRATEGIES
- **Proper Footwear**: Avoid narrow, pointed shoes and excessive high heels
- **Genetic Awareness**: Monitor for changes if family history of bunions
- **Early Intervention**: Address any toe pain or alignment changes promptly

### 📋 FOLLOW-UP
- **Routine Monitoring**: Annual foot assessment recommended
- **Watch For**: Any changes in big toe alignment or joint pain
- **Professional Care**: Consult podiatrist if toe pain or deformity develops

**Excellent News**: Your big toe alignment appears normal. Continue current foot care practices to maintain healthy toe structure."""

        return {
            "success": True,
            "recommendations": recommendations,
            "analysis_type": "hallux_valgus",
            "model_used": "Enhanced Clinical Algorithm",
            "analysis_summary": {
                "condition_detected": "hallux_valgus" if classification == "Hallux Valgus" else "normal_alignment",
                "confidence_level": confidence,
                "specific_findings": classification
            }
        }

    else:
        return {
            "success": False,
            "error": f"Invalid analysis type: {analysis_type}",
            "fallback_recommendations": "Please ensure proper analysis data is provided."
        }

def get_individual_fallback_recommendations(analysis_results: AnalysisResult, analysis_type: str) -> str:
    """
    Provide basic recommendations for individual analysis when AI service is unavailable.
    """
    if analysis_type == "flatfoot" and analysis_results.yolo_predictions is not None:
        detections = len(analysis_results.yolo_predictions)
        confidence = max(pred['confidence'] for pred in analysis_results.yolo_predictions) if detections > 0 else 0.0

        return f"""## Flatfoot Analysis - Basic Recommendations

**Detection Summary**: {detections} flatfoot detection(s) with {confidence:.2f} confidence

### Immediate Actions
- Consider arch support insoles
- Avoid prolonged standing on hard surfaces
- Choose supportive footwear with good arch support

### Basic Exercises
- Calf raises: 3 sets of 15 repetitions daily
- Toe curls: Pick up small objects with toes
- Arch stretches: Roll foot over tennis ball

### When to Seek Professional Help
- Persistent foot pain or discomfort
- Difficulty finding comfortable footwear
- Pain that worsens with activity

**Important**: This is a basic assessment. Consult a podiatrist for comprehensive evaluation and personalized treatment plan."""

    elif analysis_type == "hallux_valgus" and analysis_results.tensorflow_prediction:
        classification = analysis_results.tensorflow_prediction['class']
        confidence = analysis_results.tensorflow_prediction['confidence']

        return f"""## Hallux Valgus Analysis - Basic Recommendations

**Classification**: {classification} with {confidence:.2f} confidence

### Immediate Actions
- Choose wide-toe box shoes
- Avoid high heels and pointed shoes
- Consider bunion pads for comfort

### Basic Exercises
- Toe stretches: Spread toes apart and hold
- Big toe flexion: Move big toe up and down
- Foot massage: Gentle circular motions around bunion area

### When to Seek Professional Help
- Increasing pain or swelling
- Difficulty walking or wearing shoes
- Progressive deformity changes

**Important**: This is a basic assessment. Consult a podiatrist for comprehensive evaluation and personalized treatment plan."""

    else:
        return "Basic foot care recommendations: Maintain proper hygiene, wear supportive footwear, and consult a healthcare professional for any concerns."

def get_fallback_recommendations(analysis_results: AnalysisResult) -> str:
    """
    Provide basic recommendations when AI service is unavailable.
    """
    recommendations = "## Basic Recommendations\n\n"

    # Check for flatfoot
    flatfoot_detected = any(pred["label"] == "Flat Foot" for pred in (analysis_results.yolo_predictions or []))

    # Check for hallux valgus
    hallux_valgus_detected = (analysis_results.tensorflow_prediction and
                             analysis_results.tensorflow_prediction.get("class") == "Hallux Valgus")

    if flatfoot_detected:
        recommendations += """
### Flatfoot Detected
- Consider arch support insoles or custom orthotics
- Perform foot strengthening exercises (calf raises, toe curls)
- Choose supportive footwear with good arch support
- Avoid prolonged standing on hard surfaces
- Consult a podiatrist for comprehensive evaluation
"""

    if hallux_valgus_detected:
        recommendations += """
### Hallux Valgus (Bunion) Detected
- Wear wide, comfortable shoes with low heels
- Use bunion pads or toe spacers for comfort
- Apply ice for pain and swelling
- Consider night splints to slow progression
- Avoid tight, pointed shoes
- Consult an orthopedic specialist if pain persists
"""

    if not flatfoot_detected and not hallux_valgus_detected:
        recommendations += """
### Normal Foot Structure Detected
- Maintain good foot hygiene
- Wear properly fitting, supportive shoes
- Perform regular foot exercises to maintain strength
- Monitor for any changes in foot structure or pain
- Continue regular foot health check-ups
"""

    recommendations += """
### General Foot Health Tips
- Maintain a healthy weight to reduce foot pressure
- Stretch and strengthen foot muscles regularly
- Replace worn-out shoes promptly
- Seek professional medical advice for persistent pain or concerns

**Important**: These are general recommendations. Always consult with a healthcare professional for personalized medical advice.
"""

    return recommendations

# YOLOv8 prediction endpoint
@app.post("/predict/yolo")
async def predict_yolo(file: UploadFile = File(...)):
    """
    Predict endpoint for YOLOv8 object detection.
    - Saves the annotated image.
    - Returns predictions and the URL of the annotated image.
    """
    # Read the uploaded image
    image = read_file_as_image(await file.read())

    # Run YOLOv8 model prediction
    results = yolo_model.predict(image)

    # Save annotated image with a specific format (PNG)
    annotated_img_path = os.path.join(OUTPUT_DIR, f"annotated_{file.filename}")
    annotated_img_path = annotated_img_path.split('.')[0] + ".png"  # Ensure PNG format
    image_array = results[0].plot()
    
    # Save the annotated image
    Image.fromarray(image_array).convert("RGB").save(annotated_img_path, format='PNG')

    # Extract predictions
    output = []
    if results[0].boxes is not None:  # Handle cases with detections
        for box in results[0].boxes.data:
            cls = int(box[5].item())  # Class index (assumes index 5 is class)
            conf = float(box[4].item())  # Confidence score (assumes index 4 is confidence)
            bbox = [float(coord) for coord in box[:4].tolist()]  # Bounding box coordinates (assumes first 4 are xyxy)

            label = CUSTOM_LABELS.get(cls, "Unknown")
        
            output.append({
                "label": label,
                "confidence": conf,
                "bbox": bbox
            })

    # Generate AI recommendations for flatfoot analysis
    ai_recommendations = None
    try:
        # Create proper AnalysisResult object for AI recommendations
        analysis_results = AnalysisResult(
            yolo_predictions=output,
            tensorflow_prediction=None
        )

        # Get precise AI recommendations for flatfoot analysis specifically
        try:
            ai_recommendations = await get_individual_ai_recommendations(analysis_results, "flatfoot")
        except Exception as e:
            print(f"AI service timeout, using enhanced fallback: {e}")
            ai_recommendations = get_enhanced_individual_recommendations(analysis_results, "flatfoot")

    except Exception as e:
        print(f"AI recommendations failed for flatfoot: {e}")
        ai_recommendations = {
            "success": False,
            "error": str(e),
            "fallback_recommendations": "Please consult with a healthcare professional for proper foot assessment."
        }

    return JSONResponse({
        "predictions": output,
        "annotated_image_url": f"http://localhost:8000/annotated_images/{os.path.basename(annotated_img_path)}",
        "ai_recommendations": ai_recommendations,
        "analysis_type": "flatfoot",
        "timestamp": datetime.now().isoformat()
    })

# TensorFlow prediction endpoint
@app.post("/predict/tensorflow")
async def predict_tensorflow(file: UploadFile = File(...)):
    """
    Predict endpoint for TensorFlow model prediction.
    """
    if tensorflow_model is None:
        return JSONResponse(
            {"error": "TensorFlow model is not available. Please check model file and compatibility."},
            status_code=500
        )

    try:
        # Read the uploaded image
        image = read_file_as_image(await file.read())
        img_batch = np.expand_dims(image, 0)

        predictions = tensorflow_model.predict(img_batch)

        predicted_class = CLASS_NAMES[np.argmax(predictions[0])]
        confidence = np.max(predictions[0])

        # Generate AI recommendations for hallux valgus analysis
        ai_recommendations = None
        try:
            # Create proper AnalysisResult object for AI recommendations
            analysis_results = AnalysisResult(
                yolo_predictions=None,
                tensorflow_prediction={
                    "class": predicted_class,
                    "confidence": float(confidence)
                }
            )

            # Get precise AI recommendations for hallux valgus analysis specifically
            try:
                ai_recommendations = await get_individual_ai_recommendations(analysis_results, "hallux_valgus")
            except Exception as e:
                print(f"AI service timeout, using enhanced fallback: {e}")
                ai_recommendations = get_enhanced_individual_recommendations(analysis_results, "hallux_valgus")

        except Exception as e:
            print(f"AI recommendations failed for hallux valgus: {e}")
            ai_recommendations = {
                "success": False,
                "error": str(e),
                "fallback_recommendations": "Please consult with a healthcare professional for proper foot assessment."
            }

        return {
            'class': predicted_class,
            'confidence': float(confidence),
            'ai_recommendations': ai_recommendations,
            'analysis_type': 'hallux_valgus',
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return JSONResponse(
            {"error": f"Prediction failed: {str(e)}"},
            status_code=500
        )

def determine_combined_risk(flatfoot_analysis: Dict[str, Any], hallux_valgus_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine combined risk assessment when both conditions are analyzed.
    """
    risk_assessment = {
        "overall_risk_level": "Low",
        "risk_factors": [],
        "combined_conditions": False,
        "priority_condition": None,
        "recommendations_priority": "preventive"
    }

    # Check if both analyses were successful
    flatfoot_success = flatfoot_analysis.get("status") == "success"
    hallux_valgus_success = hallux_valgus_analysis.get("status") == "success"

    if not (flatfoot_success and hallux_valgus_success):
        risk_assessment["overall_risk_level"] = "Unknown"
        risk_assessment["risk_factors"].append("Incomplete analysis due to model errors")
        return risk_assessment

    # Analyze flatfoot findings
    flatfoot_detected = flatfoot_analysis.get("primary_finding") == "Flat Foot"
    flatfoot_confidence = flatfoot_analysis.get("confidence_score", 0)

    # Analyze hallux valgus findings
    hallux_valgus_detected = hallux_valgus_analysis.get("primary_finding") == "Hallux Valgus"
    hallux_valgus_confidence = hallux_valgus_analysis.get("confidence_score", 0)

    # Determine combined conditions
    if flatfoot_detected and hallux_valgus_detected:
        risk_assessment["combined_conditions"] = True
        risk_assessment["overall_risk_level"] = "High"
        risk_assessment["risk_factors"].extend([
            "Multiple foot deformities present",
            "Increased risk of biomechanical complications",
            "Higher likelihood of progressive symptoms"
        ])
        risk_assessment["recommendations_priority"] = "immediate_intervention"

        # Determine priority condition based on confidence
        if flatfoot_confidence > hallux_valgus_confidence:
            risk_assessment["priority_condition"] = "Flatfoot"
        else:
            risk_assessment["priority_condition"] = "Hallux Valgus"

    elif flatfoot_detected:
        risk_assessment["overall_risk_level"] = "Moderate" if flatfoot_confidence > 0.8 else "Mild"
        risk_assessment["risk_factors"].append("Flatfoot condition detected")
        risk_assessment["priority_condition"] = "Flatfoot"
        risk_assessment["recommendations_priority"] = "conservative_treatment"

    elif hallux_valgus_detected:
        risk_assessment["overall_risk_level"] = "Moderate" if hallux_valgus_confidence > 0.8 else "Mild"
        risk_assessment["risk_factors"].append("Hallux Valgus condition detected")
        risk_assessment["priority_condition"] = "Hallux Valgus"
        risk_assessment["recommendations_priority"] = "conservative_treatment"

    else:
        risk_assessment["overall_risk_level"] = "Low"
        risk_assessment["risk_factors"].append("No significant deformities detected")
        risk_assessment["recommendations_priority"] = "preventive"

    return risk_assessment

@app.post("/ai-recommendations")
async def get_recommendations(request: AIRecommendationRequest):
    """
    Get AI-powered recommendations based on analysis results.
    """
    try:
        recommendations = await get_ai_recommendations(
            request.analysis_results,
            request.patient_info
        )
        return JSONResponse(recommendations)
    except Exception as e:
        return JSONResponse(
            {"error": f"Failed to get AI recommendations: {str(e)}"},
            status_code=500
        )

@app.post("/predict/combined")
async def predict_combined(file: UploadFile = File(...)):
    """
    Enhanced combined prediction endpoint that runs both models simultaneously
    and provides comprehensive dual analysis for Hallux Valgus and Flatfoot detection.
    """
    try:
        # Read the uploaded image
        image_data = await file.read()
        image = read_file_as_image(image_data)

        # Initialize results structure
        analysis_timestamp = datetime.now().isoformat()

        # Run YOLO prediction for Flatfoot detection
        flatfoot_analysis = {
            "condition": "Flatfoot",
            "model_type": "YOLO v8",
            "status": "success",
            "predictions": [],
            "primary_finding": None,
            "confidence_score": 0.0
        }

        try:
            yolo_results = yolo_model.predict(image)

            # Save annotated image
            annotated_img_path = os.path.join(OUTPUT_DIR, f"combined_annotated_{file.filename}")
            annotated_img_path = annotated_img_path.split('.')[0] + ".png"
            image_array = yolo_results[0].plot()
            Image.fromarray(image_array).convert("RGB").save(annotated_img_path, format='PNG')

            # Extract YOLO predictions
            if yolo_results[0].boxes is not None:
                for box in yolo_results[0].boxes.data:
                    cls = int(box[5].item())
                    conf = float(box[4].item())
                    bbox = [float(coord) for coord in box[:4].tolist()]
                    label = CUSTOM_LABELS.get(cls, "Unknown")

                    flatfoot_analysis["predictions"].append({
                        "label": label,
                        "confidence": conf,
                        "bbox": bbox,
                        "area_affected": f"Region {len(flatfoot_analysis['predictions']) + 1}"
                    })

                # Determine primary finding for flatfoot
                if flatfoot_analysis["predictions"]:
                    best_prediction = max(flatfoot_analysis["predictions"], key=lambda x: x["confidence"])
                    flatfoot_analysis["primary_finding"] = best_prediction["label"]
                    flatfoot_analysis["confidence_score"] = best_prediction["confidence"]
                else:
                    flatfoot_analysis["primary_finding"] = "Normal"
                    flatfoot_analysis["confidence_score"] = 0.95  # High confidence for normal when no detections
            else:
                flatfoot_analysis["primary_finding"] = "Normal"
                flatfoot_analysis["confidence_score"] = 0.95

        except Exception as e:
            flatfoot_analysis["status"] = "error"
            flatfoot_analysis["error"] = str(e)
            print(f"YOLO prediction error: {e}")

        # Run TensorFlow prediction for Hallux Valgus detection
        hallux_valgus_analysis = {
            "condition": "Hallux Valgus",
            "model_type": "TensorFlow/Keras",
            "status": "success",
            "primary_finding": None,
            "confidence_score": 0.0,
            "all_class_probabilities": {}
        }

        try:
            if tensorflow_model is not None:
                img_batch = np.expand_dims(image, 0)
                predictions = tensorflow_model.predict(img_batch)
                predicted_class = CLASS_NAMES[np.argmax(predictions[0])]
                confidence = np.max(predictions[0])

                hallux_valgus_analysis["primary_finding"] = predicted_class
                hallux_valgus_analysis["confidence_score"] = float(confidence)

                # Store all class probabilities for detailed analysis
                for i, class_name in enumerate(CLASS_NAMES):
                    hallux_valgus_analysis["all_class_probabilities"][class_name] = float(predictions[0][i])
            else:
                hallux_valgus_analysis["status"] = "error"
                hallux_valgus_analysis["error"] = "TensorFlow model not loaded"

        except Exception as e:
            hallux_valgus_analysis["status"] = "error"
            hallux_valgus_analysis["error"] = str(e)
            print(f"TensorFlow prediction error: {e}")

        # Create comprehensive analysis summary
        combined_analysis = {
            "analysis_timestamp": analysis_timestamp,
            "image_filename": file.filename,
            "flatfoot_analysis": flatfoot_analysis,
            "hallux_valgus_analysis": hallux_valgus_analysis,
            "combined_risk_assessment": determine_combined_risk(flatfoot_analysis, hallux_valgus_analysis),
            "annotated_image_url": f"http://localhost:8000/annotated_images/{os.path.basename(annotated_img_path)}" if 'annotated_img_path' in locals() else None
        }

        # Prepare analysis results for AI recommendations (maintain backward compatibility)
        analysis_results = AnalysisResult(
            yolo_predictions=flatfoot_analysis["predictions"],
            tensorflow_prediction={
                'class': hallux_valgus_analysis["primary_finding"],
                'confidence': hallux_valgus_analysis["confidence_score"]
            } if hallux_valgus_analysis["status"] == "success" else None
        )

        # Get AI recommendations
        ai_recommendations = await get_ai_recommendations(analysis_results)

        # Enhanced response with dual analysis structure
        return JSONResponse({
            "analysis_type": "dual_comprehensive",
            "combined_analysis": combined_analysis,
            "ai_recommendations": ai_recommendations,
            # Backward compatibility fields
            "yolo_predictions": flatfoot_analysis["predictions"],
            "tensorflow_prediction": {
                'class': hallux_valgus_analysis["primary_finding"],
                'confidence': hallux_valgus_analysis["confidence_score"]
            } if hallux_valgus_analysis["status"] == "success" else None,
            "annotated_image_url": combined_analysis["annotated_image_url"]
        })

    except Exception as e:
        return JSONResponse(
            {"error": f"Combined prediction failed: {str(e)}"},
            status_code=500
        )

@app.get("/annotated_images/{image_path:path}")
async def serve_image(image_path: str):
    """
    Endpoint to serve annotated images.
    """
    full_path = os.path.join(OUTPUT_DIR, image_path)

    # Check if the image exists
    if os.path.exists(full_path):
        return FileResponse(full_path)

    return JSONResponse({"error": "Image not found"}, status_code=404)

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)

