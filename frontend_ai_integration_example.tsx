// Example React component for AI recommendations integration
// This file shows how to integrate the AI recommendations into your React frontend

import React, { useState } from 'react';
import axios from 'axios';

interface AnalysisResult {
  yolo_predictions?: Array<{
    label: string;
    confidence: number;
    bbox: number[];
  }>;
  tensorflow_prediction?: {
    class: string;
    confidence: number;
  };
}

interface AIRecommendation {
  success: boolean;
  recommendations?: string;
  model_used?: string;
  analysis_summary?: {
    flatfoot_detected: boolean;
    hallux_valgus_detected: boolean;
  };
  error?: string;
  fallback_recommendations?: string;
}

interface PatientInfo {
  age?: number;
  weight?: string;
  activity_level?: string;
  symptoms?: string;
}

const AIRecommendationsComponent: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(null);
  const [aiRecommendations, setAiRecommendations] = useState<AIRecommendation | null>(null);
  const [patientInfo, setPatientInfo] = useState<PatientInfo>({});
  const [loading, setLoading] = useState(false);
  const [annotatedImageUrl, setAnnotatedImageUrl] = useState<string>('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleCombinedAnalysis = async () => {
    if (!selectedFile) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post('http://localhost:8000/predict/combined', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      setAnalysisResults({
        yolo_predictions: data.yolo_predictions,
        tensorflow_prediction: data.tensorflow_prediction,
      });
      setAiRecommendations(data.ai_recommendations);
      setAnnotatedImageUrl(data.annotated_image_url);
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGetRecommendations = async () => {
    if (!analysisResults) return;

    setLoading(true);
    try {
      const requestData = {
        analysis_results: analysisResults,
        patient_info: patientInfo,
      };

      const response = await axios.post('http://localhost:8000/ai-recommendations', requestData);
      setAiRecommendations(response.data);
    } catch (error) {
      console.error('Failed to get AI recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatRecommendations = (text: string) => {
    // Simple markdown-like formatting for display
    return text
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('##')) {
          return <h3 key={index} className="text-lg font-bold mt-4 mb-2">{line.replace('##', '').trim()}</h3>;
        }
        if (line.startsWith('###')) {
          return <h4 key={index} className="text-md font-semibold mt-3 mb-1">{line.replace('###', '').trim()}</h4>;
        }
        if (line.startsWith('-')) {
          return <li key={index} className="ml-4">{line.replace('-', '').trim()}</li>;
        }
        if (line.trim()) {
          return <p key={index} className="mb-2">{line}</p>;
        }
        return <br key={index} />;
      });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold text-center">AI-Powered Foot Analysis</h1>
      
      {/* File Upload Section */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Upload Foot Image</h2>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="mb-4 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        <button
          onClick={handleCombinedAnalysis}
          disabled={!selectedFile || loading}
          className="bg-blue-500 text-white px-6 py-2 rounded-lg disabled:bg-gray-300 hover:bg-blue-600"
        >
          {loading ? 'Analyzing...' : 'Analyze with AI Recommendations'}
        </button>
      </div>

      {/* Patient Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Patient Information (Optional)</h2>
        <div className="grid grid-cols-2 gap-4">
          <input
            type="number"
            placeholder="Age"
            value={patientInfo.age || ''}
            onChange={(e) => setPatientInfo({...patientInfo, age: parseInt(e.target.value)})}
            className="border rounded-lg px-3 py-2"
          />
          <input
            type="text"
            placeholder="Weight (e.g., 70kg)"
            value={patientInfo.weight || ''}
            onChange={(e) => setPatientInfo({...patientInfo, weight: e.target.value})}
            className="border rounded-lg px-3 py-2"
          />
          <select
            value={patientInfo.activity_level || ''}
            onChange={(e) => setPatientInfo({...patientInfo, activity_level: e.target.value})}
            className="border rounded-lg px-3 py-2"
          >
            <option value="">Activity Level</option>
            <option value="low">Low</option>
            <option value="moderate">Moderate</option>
            <option value="high">High</option>
          </select>
          <input
            type="text"
            placeholder="Symptoms"
            value={patientInfo.symptoms || ''}
            onChange={(e) => setPatientInfo({...patientInfo, symptoms: e.target.value})}
            className="border rounded-lg px-3 py-2"
          />
        </div>
        {analysisResults && (
          <button
            onClick={handleGetRecommendations}
            disabled={loading}
            className="mt-4 bg-green-500 text-white px-6 py-2 rounded-lg disabled:bg-gray-300 hover:bg-green-600"
          >
            {loading ? 'Getting Recommendations...' : 'Get Updated AI Recommendations'}
          </button>
        )}
      </div>

      {/* Analysis Results */}
      {analysisResults && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
          
          {/* YOLO Results */}
          {analysisResults.yolo_predictions && analysisResults.yolo_predictions.length > 0 && (
            <div className="mb-4">
              <h3 className="text-lg font-medium mb-2">Flatfoot Detection (YOLO)</h3>
              {analysisResults.yolo_predictions.map((pred, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded mb-2">
                  <span className={`font-semibold ${pred.label === 'Flat Foot' ? 'text-red-600' : 'text-green-600'}`}>
                    {pred.label}
                  </span>
                  <span className="ml-2 text-gray-600">
                    (Confidence: {(pred.confidence * 100).toFixed(1)}%)
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* TensorFlow Results */}
          {analysisResults.tensorflow_prediction && (
            <div className="mb-4">
              <h3 className="text-lg font-medium mb-2">Hallux Valgus Detection (TensorFlow)</h3>
              <div className="bg-gray-50 p-3 rounded">
                <span className={`font-semibold ${analysisResults.tensorflow_prediction.class === 'Hallux Valgus' ? 'text-red-600' : 'text-green-600'}`}>
                  {analysisResults.tensorflow_prediction.class}
                </span>
                <span className="ml-2 text-gray-600">
                  (Confidence: {(analysisResults.tensorflow_prediction.confidence * 100).toFixed(1)}%)
                </span>
              </div>
            </div>
          )}

          {/* Annotated Image */}
          {annotatedImageUrl && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Annotated Image</h3>
              <img src={annotatedImageUrl} alt="Annotated foot analysis" className="max-w-full h-auto rounded-lg" />
            </div>
          )}
        </div>
      )}

      {/* AI Recommendations */}
      {aiRecommendations && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">
            AI Recommendations
            {aiRecommendations.model_used && (
              <span className="text-sm text-gray-500 ml-2">
                (Powered by {aiRecommendations.model_used})
              </span>
            )}
          </h2>
          
          {aiRecommendations.success ? (
            <div className="prose max-w-none">
              {aiRecommendations.recommendations && formatRecommendations(aiRecommendations.recommendations)}
            </div>
          ) : (
            <div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <p className="text-yellow-800">
                  AI service temporarily unavailable. Showing basic recommendations:
                </p>
              </div>
              {aiRecommendations.fallback_recommendations && (
                <div className="prose max-w-none">
                  {formatRecommendations(aiRecommendations.fallback_recommendations)}
                </div>
              )}
              {aiRecommendations.error && (
                <p className="text-red-600 text-sm mt-2">Error: {aiRecommendations.error}</p>
              )}
            </div>
          )}
          
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              <strong>Medical Disclaimer:</strong> These recommendations are for informational purposes only. 
              Always consult with qualified healthcare providers for medical decisions.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIRecommendationsComponent;
