#!/usr/bin/env python3
"""
Quick test for AI recommendations endpoint
"""
import requests
import json

def test_ai_endpoint():
    """Test the AI recommendations endpoint with a simple case"""
    
    print("🤖 Quick AI Recommendations Test")
    print("=" * 40)
    
    # Simple test data
    test_data = {
        "analysis_results": {
            "yolo_predictions": [
                {
                    "label": "Normal",
                    "confidence": 0.95,
                    "bbox": [100, 100, 200, 200]
                }
            ],
            "tensorflow_prediction": {
                "class": "Normal",
                "confidence": 0.93
            }
        },
        "patient_info": {
            "age": 30
        }
    }
    
    try:
        print("📤 Sending request...")
        response = requests.post(
            "http://127.0.0.1:8000/ai-recommendations",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=60  # Longer timeout for AI processing
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"Model used: {result.get('model_used', 'Unknown')}")
            print(f"Success: {result.get('success', False)}")
            
            if result.get('recommendations'):
                print("\n📋 AI Recommendations (first 200 chars):")
                print(result['recommendations'][:200] + "...")
            
            if result.get('fallback_recommendations'):
                print("\n🛡️ Fallback recommendations available")
                
        else:
            print(f"❌ Failed: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout - AI service is taking too long")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_server_health():
    """Quick health check"""
    try:
        response = requests.get("http://127.0.0.1:8000/ping", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    if test_server_health():
        print("✅ Server is running")
        test_ai_endpoint()
    else:
        print("❌ Server not responding")
        print("Make sure FastAPI server is running: npm run fastapi-dev")
