#!/usr/bin/env python3
"""
Test the precise individual AI recommendations
"""

import requests
import io
from PIL import Image
import numpy as np
import json

def create_test_image():
    """Create a simple test image"""
    img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_individual_ai_recommendations():
    """Test individual AI recommendations for precise analysis"""
    
    print("🧪 Testing Precise Individual AI Recommendations")
    print("=" * 60)
    
    # Test Flatfoot Analysis
    print("\n🦶 Testing Flatfoot Individual Analysis...")
    test_image = create_test_image()
    files = {'file': ('test.jpg', test_image, 'image/jpeg')}
    
    try:
        response = requests.post(
            "http://localhost:8000/predict/yolo",
            files=files,
            timeout=45
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Flatfoot endpoint: SUCCESS")
            
            if 'ai_recommendations' in result:
                ai_rec = result['ai_recommendations']
                print(f"📊 AI Success: {ai_rec.get('success', False)}")
                
                if ai_rec.get('success'):
                    recommendations = ai_rec.get('recommendations', '')
                    print(f"📝 Recommendations Length: {len(recommendations)} characters")
                    print(f"🎯 Analysis Type: {ai_rec.get('analysis_type', 'Not specified')}")
                    
                    # Show first 200 characters of recommendations
                    print(f"📋 Sample Recommendations:")
                    print("-" * 40)
                    print(recommendations[:300] + "..." if len(recommendations) > 300 else recommendations)
                    print("-" * 40)
                    
                    # Check for specific flatfoot content
                    flatfoot_keywords = ['flatfoot', 'arch', 'orthotic', 'support']
                    found_keywords = [kw for kw in flatfoot_keywords if kw.lower() in recommendations.lower()]
                    print(f"🔍 Flatfoot-specific keywords found: {found_keywords}")
                    
                else:
                    print(f"❌ AI Error: {ai_rec.get('error', 'Unknown error')}")
                    if 'fallback_recommendations' in ai_rec:
                        print(f"🔄 Fallback provided: {len(ai_rec['fallback_recommendations'])} characters")
            else:
                print("❌ No AI recommendations in response")
                
        else:
            print(f"❌ Flatfoot endpoint error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Flatfoot test failed: {e}")
    
    print("\n" + "=" * 60)
    
    # Test Hallux Valgus Analysis
    print("\n🦶 Testing Hallux Valgus Individual Analysis...")
    test_image = create_test_image()
    files = {'file': ('test.jpg', test_image, 'image/jpeg')}
    
    try:
        response = requests.post(
            "http://localhost:8000/predict/tensorflow",
            files=files,
            timeout=45
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Hallux Valgus endpoint: SUCCESS")
            
            if 'ai_recommendations' in result:
                ai_rec = result['ai_recommendations']
                print(f"📊 AI Success: {ai_rec.get('success', False)}")
                
                if ai_rec.get('success'):
                    recommendations = ai_rec.get('recommendations', '')
                    print(f"📝 Recommendations Length: {len(recommendations)} characters")
                    print(f"🎯 Analysis Type: {ai_rec.get('analysis_type', 'Not specified')}")
                    
                    # Show first 200 characters of recommendations
                    print(f"📋 Sample Recommendations:")
                    print("-" * 40)
                    print(recommendations[:300] + "..." if len(recommendations) > 300 else recommendations)
                    print("-" * 40)
                    
                    # Check for specific hallux valgus content
                    hv_keywords = ['hallux valgus', 'bunion', 'big toe', 'deformity']
                    found_keywords = [kw for kw in hv_keywords if kw.lower() in recommendations.lower()]
                    print(f"🔍 Hallux Valgus-specific keywords found: {found_keywords}")
                    
                else:
                    print(f"❌ AI Error: {ai_rec.get('error', 'Unknown error')}")
                    if 'fallback_recommendations' in ai_rec:
                        print(f"🔄 Fallback provided: {len(ai_rec['fallback_recommendations'])} characters")
            else:
                print("❌ No AI recommendations in response")
                
        else:
            print(f"❌ Hallux Valgus endpoint error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Hallux Valgus test failed: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print("- Individual AI recommendations are now condition-specific")
    print("- Each analysis provides precise, targeted recommendations")
    print("- Fallback recommendations are also condition-specific")
    print("✅ Ready for frontend testing!")

if __name__ == "__main__":
    test_individual_ai_recommendations()
