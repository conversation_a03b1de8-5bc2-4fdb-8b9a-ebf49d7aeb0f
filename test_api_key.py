#!/usr/bin/env python3
"""
Test if API key is properly loaded
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check API key
api_key = os.getenv("OPENROUTER_API_KEY")
print(f"API Key loaded: {api_key[:20]}..." if api_key else "API Key not found")

# Test a simple request to OpenRouter
import httpx
import asyncio

async def test_openrouter_connection():
    """Test basic connection to OpenRouter API"""
    
    if not api_key or api_key == "your-openrouter-api-key-here":
        print("❌ API key not configured properly")
        return
    
    print("🔑 Testing OpenRouter API connection...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "http://localhost:8000",
                    "X-Title": "Foot Deformity Analysis"
                },
                json={
                    "model": "deepseek/deepseek-r1-0528:free",
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello, this is a test message. Please respond with 'API connection successful'."
                        }
                    ],
                    "max_tokens": 50,
                    "temperature": 0.1
                }
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ OpenRouter API connection successful!")
                print(f"Response: {result.get('choices', [{}])[0].get('message', {}).get('content', 'No content')}")
                return True
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing OpenRouter API Key Configuration")
    print("=" * 50)
    
    # Run the async test
    result = asyncio.run(test_openrouter_connection())
    
    if result:
        print("\n✅ API key is working correctly!")
        print("You can now use the AI recommendations feature.")
    else:
        print("\n❌ API key test failed.")
        print("Please check your API key and internet connection.")
