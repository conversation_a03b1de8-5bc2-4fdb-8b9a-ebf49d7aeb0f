import React from 'react';
import { 
  <PERSON>eth<PERSON><PERSON>, 
  AlertTriangle, 
  Clock, 
  Activity, 
  Shield, 
  Calendar,
  BookOpen,
  CheckCircle,
  AlertCircle,
  Info,
  Heart,
  Target,
  Zap
} from 'lucide-react';

interface AIRecommendationsProps {
  recommendations: string;
  analysisType?: string;
}

const AIRecommendations: React.FC<AIRecommendationsProps> = ({ 
  recommendations, 
  analysisType = "Comprehensive Analysis" 
}) => {
  // Parse the recommendations into structured sections
  const parseRecommendations = (text: string) => {
    const sections: { [key: string]: string[] } = {};
    const lines = text.split('\n');
    let currentSection = '';
    let currentSubsection = '';
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('## ')) {
        currentSection = trimmedLine.replace('## ', '').trim();
        currentSubsection = '';
        sections[currentSection] = [];
      } else if (trimmedLine.startsWith('### ')) {
        currentSubsection = trimmedLine.replace('### ', '').trim();
        if (currentSection) {
          sections[currentSection].push(`**${currentSubsection}**`);
        }
      } else if (trimmedLine.startsWith('- ') && currentSection) {
        sections[currentSection].push(trimmedLine.replace('- ', '• '));
      } else if (trimmedLine.startsWith('* ') && currentSection) {
        sections[currentSection].push(trimmedLine.replace('* ', '• '));
      } else if (trimmedLine && !trimmedLine.startsWith('#') && currentSection) {
        sections[currentSection].push(trimmedLine);
      }
    });
    
    return sections;
  };

  const sections = parseRecommendations(recommendations);

  // Get severity level from the text
  const getSeverityInfo = () => {
    const severityText = sections['SEVERITY ASSESSMENT']?.join(' ') || '';
    
    if (severityText.toLowerCase().includes('severe')) {
      return { level: 'Severe', color: 'red', icon: AlertTriangle };
    } else if (severityText.toLowerCase().includes('moderate')) {
      return { level: 'Moderate', color: 'yellow', icon: AlertCircle };
    } else if (severityText.toLowerCase().includes('mild')) {
      return { level: 'Mild', color: 'blue', icon: Info };
    } else {
      return { level: 'Normal', color: 'green', icon: CheckCircle };
    }
  };

  const severity = getSeverityInfo();

  // Section configurations with icons and colors
  const sectionConfigs = {
    'DIAGNOSIS SUMMARY': { icon: Stethoscope, color: 'blue', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
    'SEVERITY ASSESSMENT': { icon: severity.icon, color: severity.color, bgColor: `bg-${severity.color}-50`, borderColor: `border-${severity.color}-200` },
    'TREATMENT RECOMMENDATIONS': { icon: Heart, color: 'red', bgColor: 'bg-red-50', borderColor: 'border-red-200' },
    'PREVENTION STRATEGIES': { icon: Shield, color: 'green', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
    'FOLLOW-UP TIMELINE': { icon: Calendar, color: 'purple', bgColor: 'bg-purple-50', borderColor: 'border-purple-200' },
    'LIFESTYLE MODIFICATIONS': { icon: Activity, color: 'indigo', bgColor: 'bg-indigo-50', borderColor: 'border-indigo-200' },
    'PATIENT EDUCATION': { icon: BookOpen, color: 'gray', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' }
  };

  const renderSection = (title: string, content: string[]) => {
    if (!content || content.length === 0) return null;
    
    const config = sectionConfigs[title as keyof typeof sectionConfigs] || {
      icon: Info,
      color: 'gray',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    };
    
    const IconComponent = config.icon;

    return (
      <div key={title} className={`${config.bgColor} ${config.borderColor} border rounded-lg p-6 mb-6`}>
        <div className="flex items-center mb-4">
          <IconComponent className={`h-6 w-6 text-${config.color}-600 mr-3`} />
          <h3 className={`text-xl font-bold text-${config.color}-900`}>{title}</h3>
          {title === 'SEVERITY ASSESSMENT' && (
            <span className={`ml-auto px-3 py-1 rounded-full text-sm font-medium bg-${severity.color}-100 text-${severity.color}-800`}>
              {severity.level}
            </span>
          )}
        </div>
        
        <div className="space-y-3">
          {content.map((item, index) => {
            if (item.startsWith('**') && item.endsWith('**')) {
              // Subsection header
              return (
                <h4 key={index} className={`text-lg font-semibold text-${config.color}-800 mt-4 mb-2 flex items-center`}>
                  <Target className={`h-4 w-4 text-${config.color}-600 mr-2`} />
                  {item.replace(/\*\*/g, '')}
                </h4>
              );
            } else if (item.startsWith('• ')) {
              // Bullet point
              return (
                <div key={index} className="flex items-start">
                  <Zap className={`h-4 w-4 text-${config.color}-500 mr-2 mt-0.5 flex-shrink-0`} />
                  <span className="text-gray-700">{item.replace('• ', '')}</span>
                </div>
              );
            } else {
              // Regular paragraph
              return (
                <p key={index} className="text-gray-700 leading-relaxed">
                  {item}
                </p>
              );
            }
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg shadow-lg border border-blue-200">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Stethoscope className="h-8 w-8 mr-3" />
            <div>
              <h2 className="text-2xl font-bold">AI Medical Recommendations</h2>
              <p className="text-blue-100 mt-1">{analysisType}</p>
            </div>
          </div>
          <div className="text-right">
            <span className="text-xs bg-white/20 px-3 py-1 rounded-full">
              Powered by DeepSeek AI
            </span>
            <div className="text-xs text-blue-100 mt-1">
              Generated: {new Date().toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Quick Summary Card */}
        {sections['DIAGNOSIS SUMMARY'] && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex items-center mb-3">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Quick Summary</h3>
            </div>
            <p className="text-gray-700 leading-relaxed">
              {sections['DIAGNOSIS SUMMARY'][0]}
            </p>
          </div>
        )}

        {/* Severity Alert */}
        {sections['SEVERITY ASSESSMENT'] && severity.level !== 'Normal' && (
          <div className={`bg-${severity.color}-50 border border-${severity.color}-200 rounded-lg p-4 mb-6`}>
            <div className="flex items-center">
              <severity.icon className={`h-6 w-6 text-${severity.color}-600 mr-3`} />
              <div>
                <h4 className={`font-semibold text-${severity.color}-900`}>
                  Severity Level: {severity.level}
                </h4>
                <p className={`text-${severity.color}-700 text-sm mt-1`}>
                  {severity.level === 'Severe' && 'Immediate medical attention recommended'}
                  {severity.level === 'Moderate' && 'Professional consultation advised'}
                  {severity.level === 'Mild' && 'Monitor condition and follow recommendations'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Main Sections */}
        <div className="space-y-6">
          {Object.entries(sections).map(([title, content]) => 
            renderSection(title, content)
          )}
        </div>

        {/* Medical Disclaimer */}
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-yellow-900 mb-2">Important Medical Disclaimer</h4>
              <p className="text-yellow-800 text-sm leading-relaxed">
                These AI-generated recommendations are for informational purposes only and should not replace professional medical advice. 
                Always consult with qualified healthcare providers for accurate diagnosis, treatment plans, and medical decisions. 
                If you experience severe symptoms or your condition worsens, seek immediate medical attention.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex flex-wrap gap-3">
          <button 
            onClick={() => window.print()}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <BookOpen className="h-4 w-4 mr-2" />
            Print Report
          </button>
          <button 
            onClick={() => {
              const text = recommendations;
              navigator.clipboard.writeText(text);
              alert('Recommendations copied to clipboard!');
            }}
            className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Target className="h-4 w-4 mr-2" />
            Copy Text
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIRecommendations;
