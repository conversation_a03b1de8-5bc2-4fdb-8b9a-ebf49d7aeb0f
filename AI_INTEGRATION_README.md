# AI Agent Integration for Foot Deformity Analysis

This document explains how to use the AI agent integration that provides intelligent recommendations based on foot deformity analysis results.

## Overview

The AI agent uses OpenRouter API with DeepSeek's free model (`deepseek/deepseek-r1-0528:free`) to analyze foot deformity detection results and provide comprehensive medical recommendations.

## Features

- **Intelligent Analysis**: AI interprets both YOLO (flatfoot) and TensorFlow (hallux valgus) detection results
- **Personalized Recommendations**: Considers patient information for tailored advice
- **Comprehensive Guidance**: Provides treatment options, prevention tips, and follow-up recommendations
- **Fallback System**: Provides basic recommendations when AI service is unavailable
- **Professional Format**: Medical-grade recommendations suitable for healthcare contexts

## Setup

### 1. Get OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/keys)
2. Sign up for a free account
3. Generate an API key
4. Copy your API key

### 2. Configure Environment

Create a `.env` file in your project root:

```bash
# Copy from .env.example
cp .env.example .env
```

Edit `.env` and add your API key:

```env
OPENROUTER_API_KEY=your-actual-api-key-here
```

### 3. Install Dependencies

The required `httpx` library should already be installed. If not:

```bash
pip install httpx
```

## API Endpoints

### 1. AI Recommendations Endpoint

**POST** `/ai-recommendations`

Get AI-powered recommendations based on analysis results.

**Request Body:**
```json
{
  "analysis_results": {
    "yolo_predictions": [
      {
        "label": "Flat Foot",
        "confidence": 0.85,
        "bbox": [100, 100, 200, 200]
      }
    ],
    "tensorflow_prediction": {
      "class": "Hallux Valgus",
      "confidence": 0.92
    }
  },
  "patient_info": {
    "age": 35,
    "weight": "70kg",
    "activity_level": "moderate",
    "symptoms": "foot pain after walking"
  }
}
```

**Response:**
```json
{
  "success": true,
  "recommendations": "## Diagnosis Summary\n\nBased on the analysis...",
  "model_used": "deepseek/deepseek-r1-0528:free",
  "analysis_summary": {
    "flatfoot_detected": true,
    "hallux_valgus_detected": true
  }
}
```

### 2. Combined Analysis Endpoint

**POST** `/predict/combined`

Runs both detection models and automatically generates AI recommendations.

**Request:** Multipart form with image file

**Response:**
```json
{
  "yolo_predictions": [...],
  "tensorflow_prediction": {...},
  "ai_recommendations": {...},
  "annotated_image_url": "http://localhost:8000/annotated_images/..."
}
```

## Usage Examples

### Frontend Integration

```javascript
// Upload image and get combined analysis with AI recommendations
const formData = new FormData();
formData.append('file', imageFile);

const response = await fetch('http://localhost:8000/predict/combined', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('AI Recommendations:', result.ai_recommendations.recommendations);
```

### Direct AI Recommendations

```javascript
// Get recommendations for existing analysis results
const analysisData = {
  analysis_results: {
    yolo_predictions: [...],
    tensorflow_prediction: {...}
  },
  patient_info: {
    age: 35,
    symptoms: "foot pain"
  }
};

const response = await fetch('http://localhost:8000/ai-recommendations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(analysisData)
});

const recommendations = await response.json();
```

## AI Response Format

The AI provides structured recommendations including:

1. **Diagnosis Summary**: Brief interpretation of results
2. **Severity Assessment**: Mild/Moderate/Severe rating
3. **Treatment Recommendations**: 
   - Conservative treatments
   - Medical intervention criteria
4. **Prevention Tips**: Progression prevention
5. **Follow-up Recommendations**: When to seek further evaluation
6. **Lifestyle Modifications**: Daily activities and footwear advice

## Error Handling

The system includes robust error handling:

- **API Failures**: Falls back to basic recommendations
- **Model Unavailable**: Provides condition-specific guidance
- **Invalid Input**: Returns appropriate error messages
- **Rate Limiting**: Handles OpenRouter API limits gracefully

## Cost Considerations

- **DeepSeek Free Model**: No cost for basic usage
- **Rate Limits**: Free tier has usage limitations
- **Fallback System**: Ensures functionality even without API access

## Security Notes

- Store API keys securely in environment variables
- Never commit API keys to version control
- Consider implementing request rate limiting
- Validate all input data before processing

## Testing

Use the provided test script:

```bash
python test_ai_integration.py
```

Or test manually via FastAPI docs at: http://127.0.0.1:8000/docs

## Troubleshooting

### Common Issues

1. **"OpenRouter API error"**: Check your API key and internet connection
2. **"AI recommendation service error"**: Verify httpx is installed
3. **"Model not available"**: Falls back to basic recommendations automatically

### Debug Mode

Set environment variable for detailed logging:
```bash
export DEBUG=1
```

## Medical Disclaimer

The AI recommendations are for informational purposes only and should not replace professional medical advice. Always consult with qualified healthcare providers for medical decisions.
