#!/usr/bin/env python3
"""
Simple test for AI integration without external dependencies
"""
import json
import sys
import os

# Add the current directory to Python path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our AI functions directly
try:
    from api.index import get_fallback_recommendations, AnalysisResult
    print("✅ Successfully imported AI functions")
except ImportError as e:
    print(f"❌ Failed to import AI functions: {e}")
    sys.exit(1)

def test_fallback_recommendations():
    """Test the fallback recommendations system"""
    print("\n🧪 Testing Fallback Recommendations System")
    print("=" * 50)
    
    # Test case 1: Flatfoot detected
    print("\n📋 Test Case 1: Flatfoot Detected")
    analysis_flatfoot = AnalysisResult(
        yolo_predictions=[
            {
                "label": "Flat Foot",
                "confidence": 0.85,
                "bbox": [100, 100, 200, 200]
            }
        ],
        tensorflow_prediction=None
    )
    
    recommendations = get_fallback_recommendations(analysis_flatfoot)
    print("Recommendations:")
    print(recommendations[:200] + "..." if len(recommendations) > 200 else recommendations)
    
    # Test case 2: Hallux Valgus detected
    print("\n📋 Test Case 2: Hallux Valgus Detected")
    analysis_hallux = AnalysisResult(
        yolo_predictions=None,
        tensorflow_prediction={
            "class": "Hallux Valgus",
            "confidence": 0.92
        }
    )
    
    recommendations = get_fallback_recommendations(analysis_hallux)
    print("Recommendations:")
    print(recommendations[:200] + "..." if len(recommendations) > 200 else recommendations)
    
    # Test case 3: Both conditions detected
    print("\n📋 Test Case 3: Both Conditions Detected")
    analysis_both = AnalysisResult(
        yolo_predictions=[
            {
                "label": "Flat Foot",
                "confidence": 0.78,
                "bbox": [100, 100, 200, 200]
            }
        ],
        tensorflow_prediction={
            "class": "Hallux Valgus",
            "confidence": 0.89
        }
    )
    
    recommendations = get_fallback_recommendations(analysis_both)
    print("Recommendations:")
    print(recommendations[:200] + "..." if len(recommendations) > 200 else recommendations)
    
    # Test case 4: Normal feet
    print("\n📋 Test Case 4: Normal Feet")
    analysis_normal = AnalysisResult(
        yolo_predictions=[
            {
                "label": "Normal",
                "confidence": 0.95,
                "bbox": [100, 100, 200, 200]
            }
        ],
        tensorflow_prediction={
            "class": "Normal",
            "confidence": 0.93
        }
    )
    
    recommendations = get_fallback_recommendations(analysis_normal)
    print("Recommendations:")
    print(recommendations[:200] + "..." if len(recommendations) > 200 else recommendations)

def test_data_models():
    """Test the Pydantic data models"""
    print("\n🧪 Testing Data Models")
    print("=" * 30)
    
    try:
        # Test AnalysisResult model
        analysis = AnalysisResult(
            yolo_predictions=[{"label": "Flat Foot", "confidence": 0.85, "bbox": [1, 2, 3, 4]}],
            tensorflow_prediction={"class": "Normal", "confidence": 0.92}
        )
        print("✅ AnalysisResult model works correctly")
        print(f"   YOLO predictions: {len(analysis.yolo_predictions or [])} items")
        print(f"   TensorFlow prediction: {analysis.tensorflow_prediction}")
        
    except Exception as e:
        print(f"❌ Data model test failed: {e}")

def check_environment():
    """Check if environment is properly configured"""
    print("\n🧪 Checking Environment Configuration")
    print("=" * 40)
    
    # Check for OpenRouter API key
    api_key = os.getenv("OPENROUTER_API_KEY", "not-set")
    if api_key == "not-set" or api_key == "your-openrouter-api-key-here":
        print("⚠️  OpenRouter API key not configured")
        print("   Set OPENROUTER_API_KEY environment variable for full AI functionality")
        print("   Fallback recommendations will be used instead")
    else:
        print("✅ OpenRouter API key is configured")
    
    # Check required imports
    try:
        import httpx
        print("✅ httpx library available")
    except ImportError:
        print("❌ httpx library not available - install with: pip install httpx")
    
    try:
        import pydantic
        print("✅ pydantic library available")
    except ImportError:
        print("❌ pydantic library not available")

if __name__ == "__main__":
    print("🤖 AI Integration Test Suite")
    print("=" * 60)
    
    # Run all tests
    check_environment()
    test_data_models()
    test_fallback_recommendations()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print("\n💡 Next steps:")
    print("1. Set your OpenRouter API key in environment variables")
    print("2. Test the full AI integration with: python test_ai_integration.py")
    print("3. Visit http://127.0.0.1:8000/docs to test the API endpoints")
    print("4. Integrate the frontend component from frontend_ai_integration_example.tsx")
